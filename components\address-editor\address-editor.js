import AddressUtils from '../../utils/AddressUtils.js';

Component({
  properties: {
    show: {
      type: Boolean,
      value: false,
    },
    orderInfo: {
      type: Object,
      value: {},
    },
  },

  data: {
    // 地址信息
    address: '',
    addressDetail: '',
    addressRemark: '',
    latitude: null,
    longitude: null,
    addressId: null,
    
    // UI状态
    showLocationPicker: false,
    isGettingLocation: false,
  },

  observers: {
    'show,orderInfo': function(show, orderInfo) {
      if (show && orderInfo) {
        this.initAddressData(orderInfo);
      }
    },
  },

  methods: {
    // 初始化地址数据
    initAddressData(orderInfo) {
      this.setData({
        address: orderInfo.address || '',
        addressDetail: orderInfo.addressDetail || '',
        addressRemark: orderInfo.addressRemark || '',
        latitude: orderInfo.latitude || null,
        longitude: orderInfo.longitude || null,
        addressId: orderInfo.addressId || null,
      });
    },

    // 输入地址
    onAddressInput(e) {
      this.setData({
        address: e.detail.value,
      });
    },

    // 输入详细地址
    onAddressDetailInput(e) {
      this.setData({
        addressDetail: e.detail.value,
      });
    },

    // 输入地址备注
    onAddressRemarkInput(e) {
      this.setData({
        addressRemark: e.detail.value,
      });
    },

    // 获取当前位置
    async getCurrentLocation() {
      this.setData({ isGettingLocation: true });

      try {
        // 获取当前位置
        const location = await this.getLocation();

        if (location) {
          // 逆地理编码获取地址
          const address = await AddressUtils.getAddressFromLocation(
            location.latitude,
            location.longitude,
            { showLoading: true }
          );

          this.setData({
            latitude: location.latitude,
            longitude: location.longitude,
            address: address || '',
            // 获取当前位置时也更新详细地址，保持与地图选择一致
            addressDetail: address || '',
          });

          wx.showToast({
            title: '位置获取成功',
            icon: 'success',
            duration: 1500
          });
        }
      } catch (error) {
        console.error('获取位置失败:', error);

        // 检查具体的错误类型
        let errorMessage = '位置获取失败';
        if (error.errMsg) {
          if (error.errMsg.includes('auth')) {
            errorMessage = '请授权位置权限后重试';
          } else if (error.errMsg.includes('system')) {
            errorMessage = '定位服务不可用';
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      } finally {
        this.setData({ isGettingLocation: false });
      }
    },

    // 获取位置信息
    getLocation() {
      return new Promise((resolve, reject) => {
        wx.getLocation({
          type: 'gcj02',
          success: (res) => {
            resolve({
              latitude: res.latitude,
              longitude: res.longitude,
            });
          },
          fail: (error) => {
            console.error('获取位置失败:', error);
            reject(error);
          },
        });
      });
    },

    // 选择地图位置
    chooseLocationOnMap() {
      const { latitude, longitude, address } = this.data;

      // 首先检查是否支持chooseLocation API
      if (!wx.chooseLocation) {
        wx.showToast({
          title: '当前版本不支持地图选择',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      wx.chooseLocation({
        latitude: latitude || 39.908823,
        longitude: longitude || 116.397470,
        name: address || '',
        success: async (res) => {
          console.log('地图选择成功:', res);

          // 设置基本位置信息
          this.setData({
            latitude: res.latitude,
            longitude: res.longitude,
          });

          // 优先使用选择的地址信息
          let finalAddress = res.address || '';
          let finalAddressDetail = res.name || '';

          // 如果没有地址信息，尝试通过坐标反向解析
          if (!finalAddress && res.latitude && res.longitude) {
            try {
              const addressFromCoords = await AddressUtils.getAddressFromLocation(
                res.latitude,
                res.longitude,
                { showLoading: false } // 不显示加载提示
              );
              if (addressFromCoords) {
                finalAddress = addressFromCoords;
              }
            } catch (error) {
              console.warn('坐标反向解析失败:', error);
            }
          }

          // 更新地址信息
          this.setData({
            address: finalAddress,
            addressDetail: finalAddressDetail,
          });

          wx.showToast({
            title: '位置选择成功',
            icon: 'success',
            duration: 1500
          });
        },
        fail: (error) => {
          console.error('选择位置失败:', error);

          // 用户取消操作，不显示错误提示
          if (error.errMsg && error.errMsg.includes('cancel')) {
            return;
          }

          // 检查具体的错误类型并给出相应提示
          let errorMessage = '选择位置失败';
          if (error.errMsg) {
            if (error.errMsg.includes('auth') || error.errMsg.includes('deny')) {
              errorMessage = '请授权位置权限后重试';
            } else if (error.errMsg.includes('system') || error.errMsg.includes('fail')) {
              errorMessage = '地图服务暂时不可用，请稍后重试';
            } else if (error.errMsg.includes('requiredPrivateInfos')) {
              errorMessage = '应用配置错误，请联系客服';
            }
          }

          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2500
          });
        },
      });
    },

    // 地址搜索，暂未启用，有时间了进一步测试
    async searchAddress() {
      const { address } = this.data;

      if (!address.trim()) {
        wx.showToast({
          title: '请输入地址',
          icon: 'none',
        });
        return;
      }

      try {
        const location = await AddressUtils.getLocationFromAddress(address, { showLoading: true });

        if (location) {
          this.setData({
            latitude: location.lat,
            longitude: location.lng,
            // 地址搜索时也更新详细地址，保持一致性
            addressDetail: address,
          });

          wx.showToast({
            title: '地址解析成功',
            icon: 'success',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: '地址解析失败，请检查地址是否正确',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('地址搜索失败:', error);
        wx.showToast({
          title: '地址搜索失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 表单验证
    validateForm() {
      const { addressDetail, address } = this.data;
      
      if (!addressDetail.trim()) {
        wx.showToast({
          title: '请输入详细地址',
          icon: 'none',
        });
        return false;
      }

      // 检查地址长度限制
      if (address.length > 255) {
        wx.showToast({
          title: '服务地址不能超过255个字符',
          icon: 'none',
        });
        return false;
      }

      if (addressDetail.length > 255) {
        wx.showToast({
          title: '详细地址不能超过255个字符',
          icon: 'none',
        });
        return false;
      }

      return true;
    },

    // 确认修改
    onConfirm() {
      if (!this.validateForm()) {
        return;
      }

      const { address, addressDetail, addressRemark, latitude, longitude, addressId } = this.data;
      
      const addressData = {
        address: address.trim(),
        addressDetail: addressDetail.trim(),
        addressRemark: addressRemark.trim(),
        latitude,
        longitude,
        addressId,
      };

      this.triggerEvent('confirm', addressData);
    },

    // 取消修改
    onCancel() {
      this.triggerEvent('cancel');
    },

    // 阻止事件冒泡
    preventTap() {
      // 阻止点击事件冒泡
    },
  },
});
